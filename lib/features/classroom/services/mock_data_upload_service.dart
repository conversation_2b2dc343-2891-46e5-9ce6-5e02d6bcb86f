import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../repositories/classroom_repository.dart';
import '../repositories/activity_repository.dart';
import '../mock/mock_classes.dart';
import '../mock/mock_activities.dart';

/// Service for uploading mock classroom and activity data to Firebase
class MockDataUploadService {
  static final MockDataUploadService _instance =
      MockDataUploadService._internal();
  factory MockDataUploadService() => _instance;
  MockDataUploadService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  /// Upload all mock classroom data to Firebase
  Future<void> uploadMockClassrooms() async {
    try {
      _logger.i('Starting upload of mock classroom data');

      final batch = _firestore.batch();
      final classroomCollection = _firestore.collection('classrooms');

      for (final classroom in mockClassesList) {
        final docRef = classroomCollection.doc(classroom.id);
        batch.set(docRef, classroom.toJson());
      }

      await batch.commit();
      _logger.i(
        'Successfully uploaded ${mockClassesList.length} mock classrooms',
      );
    } catch (e) {
      _logger.e('Error uploading mock classrooms: $e');
      rethrow;
    }
  }

  /// Upload all mock activity data to Firebase
  Future<void> uploadMockActivities() async {
    try {
      _logger.i('Starting upload of mock activity data');

      final batch = _firestore.batch();
      final activityCollection = _firestore.collection('classroom_activities');

      for (final activity in mockActivitiesList) {
        final docRef = activityCollection.doc(activity.id);
        batch.set(docRef, activity.toJson());
      }

      await batch.commit();
      _logger.i(
        'Successfully uploaded ${mockActivitiesList.length} mock activities',
      );
    } catch (e) {
      _logger.e('Error uploading mock activities: $e');
      rethrow;
    }
  }

  /// Upload all mock data (classrooms and activities)
  Future<void> uploadAllMockData() async {
    try {
      _logger.i('Starting upload of all mock classroom data');

      // Upload classrooms first, then activities
      await uploadMockClassrooms();
      await uploadMockActivities();

      _logger.i('Successfully uploaded all mock classroom data');
    } catch (e) {
      _logger.e('Error uploading all mock data: $e');
      rethrow;
    }
  }

  /// Clear all classroom data from Firebase
  Future<void> clearAllClassroomData() async {
    try {
      _logger.i('Starting to clear all classroom data');

      // Clear classrooms
      final classroomSnapshot = await _firestore.collection('classrooms').get();

      final classroomBatch = _firestore.batch();
      for (final doc in classroomSnapshot.docs) {
        classroomBatch.delete(doc.reference);
      }
      await classroomBatch.commit();

      // Clear activities
      final activitySnapshot = await _firestore
          .collection('classroom_activities')
          .get();

      final activityBatch = _firestore.batch();
      for (final doc in activitySnapshot.docs) {
        activityBatch.delete(doc.reference);
      }
      await activityBatch.commit();

      _logger.i('Successfully cleared all classroom data');
    } catch (e) {
      _logger.e('Error clearing classroom data: $e');
      rethrow;
    }
  }

  /// Reset all classroom data (clear and re-upload)
  Future<void> resetAllClassroomData() async {
    try {
      _logger.i('Starting to reset all classroom data');

      await clearAllClassroomData();
      await uploadAllMockData();

      _logger.i('Successfully reset all classroom data');
    } catch (e) {
      _logger.e('Error resetting classroom data: $e');
      rethrow;
    }
  }

  /// Get statistics about current data in Firebase
  Future<Map<String, int>> getDataStatistics() async {
    try {
      _logger.i('Fetching data statistics');

      final classroomSnapshot = await _firestore
          .collection(ClassroomRepository._classroomsCollection)
          .get();

      final activitySnapshot = await _firestore
          .collection(ActivityRepository._activitiesCollection)
          .get();

      final stats = {
        'classrooms': classroomSnapshot.docs.length,
        'activities': activitySnapshot.docs.length,
        'mock_classrooms_available': mockClassesList.length,
        'mock_activities_available': mockActivitiesList.length,
      };

      _logger.i('Data statistics: $stats');
      return stats;
    } catch (e) {
      _logger.e('Error fetching data statistics: $e');
      rethrow;
    }
  }

  /// Validate that uploaded data matches mock data structure
  Future<bool> validateUploadedData() async {
    try {
      _logger.i('Validating uploaded data');

      // Check if all mock classrooms are uploaded
      final classroomSnapshot = await _firestore
          .collection(ClassroomRepository._classroomsCollection)
          .get();

      final uploadedClassroomIds = classroomSnapshot.docs
          .map((doc) => doc.id)
          .toSet();
      final mockClassroomIds = mockClassesList.map((c) => c.id).toSet();

      final missingClassrooms = mockClassroomIds.difference(
        uploadedClassroomIds,
      );
      if (missingClassrooms.isNotEmpty) {
        _logger.w('Missing classrooms: $missingClassrooms');
        return false;
      }

      // Check if all mock activities are uploaded
      final activitySnapshot = await _firestore
          .collection(ActivityRepository._activitiesCollection)
          .get();

      final uploadedActivityIds = activitySnapshot.docs
          .map((doc) => doc.id)
          .toSet();
      final mockActivityIds = mockActivitiesList.map((a) => a.id).toSet();

      final missingActivities = mockActivityIds.difference(uploadedActivityIds);
      if (missingActivities.isNotEmpty) {
        _logger.w('Missing activities: $missingActivities');
        return false;
      }

      _logger.i('Data validation successful');
      return true;
    } catch (e) {
      _logger.e('Error validating uploaded data: $e');
      return false;
    }
  }

  /// Upload data for a specific classroom only
  Future<void> uploadClassroomData(String classroomId) async {
    try {
      _logger.i('Uploading data for classroom: $classroomId');

      // Find the classroom in mock data
      final classroom = mockClassesList.firstWhere(
        (c) => c.id == classroomId,
        orElse: () => throw Exception('Classroom not found in mock data'),
      );

      // Upload the classroom
      await _firestore
          .collection(ClassroomRepository._classroomsCollection)
          .doc(classroom.id)
          .set(classroom.toJson());

      // Upload activities for this classroom
      final classroomActivities = mockActivitiesList
          .where((a) => a.classroomId == classroomId)
          .toList();

      final batch = _firestore.batch();
      final activityCollection = _firestore.collection(
        ActivityRepository._activitiesCollection,
      );

      for (final activity in classroomActivities) {
        final docRef = activityCollection.doc(activity.id);
        batch.set(docRef, activity.toJson());
      }

      await batch.commit();

      _logger.i(
        'Successfully uploaded classroom and ${classroomActivities.length} activities',
      );
    } catch (e) {
      _logger.e('Error uploading classroom data: $e');
      rethrow;
    }
  }

  /// Get mock data summary for debugging
  Map<String, dynamic> getMockDataSummary() {
    final classroomsByType = <String, int>{};
    for (final classroom in mockClassesList) {
      final type = classroom.type.label;
      classroomsByType[type] = (classroomsByType[type] ?? 0) + 1;
    }

    final activitiesByType = <String, int>{};
    for (final activity in mockActivitiesList) {
      final type = activity.type.label;
      activitiesByType[type] = (activitiesByType[type] ?? 0) + 1;
    }

    return {
      'total_classrooms': mockClassesList.length,
      'classrooms_by_type': classroomsByType,
      'total_activities': mockActivitiesList.length,
      'activities_by_type': activitiesByType,
      'current_user_classrooms': mockClassesList
          .where((c) => c.studentIds.contains('XRTanMcAUWSMq3mrRvve2Y9IMP12'))
          .length,
    };
  }
}
