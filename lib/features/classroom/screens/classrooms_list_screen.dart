import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../core/routes/app_routes.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/widgets/responsive/responsive_padding.dart';
import '../enums/classroom_type.dart';
import '../models/class_model.dart';
import '../mock/mock_classes.dart';
import '../widgets/classroom_card.dart';
import '../widgets/classroom_filter_chips.dart';
import '../widgets/classroom_search_bar.dart';

/// Screen displaying all enrolled classrooms with filtering and search functionality
class ClassroomsListScreen extends StatefulWidget {
  const ClassroomsListScreen({super.key});

  @override
  State<ClassroomsListScreen> createState() => _ClassroomsListScreenState();
}

class _ClassroomsListScreenState extends State<ClassroomsListScreen> {
  /// Current search query
  String _searchQuery = '';

  /// Currently selected filter type
  ClassroomType? _selectedFilter;

  /// List of all classrooms (from mock data)
  final List<ClassModel> _allClassrooms = mockClassesList;

  /// Filtered list of classrooms based on search and filter
  List<ClassModel> get _filteredClassrooms {
    var filtered = _allClassrooms;

    // Apply type filter
    if (_selectedFilter != null) {
      filtered = filtered
          .where((classroom) => classroom.type == _selectedFilter)
          .toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((classroom) {
        final query = _searchQuery.toLowerCase();
        return classroom.name.toLowerCase().contains(query) ||
            (classroom.subject?.toLowerCase().contains(query) ?? false) ||
            (classroom.description?.toLowerCase().contains(query) ?? false) ||
            (classroom.instructorDisplayName?.toLowerCase().contains(query) ??
                false);
      }).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('My Classrooms', style: theme.textTheme.titleLarge),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
      ),
      body: ResponsivePadding(
        mobile: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search bar
            ClassroomSearchBar(
              onSearchChanged: (query) {
                setState(() {
                  _searchQuery = query;
                });
              },
            ),

            SizedBox(height: 16.h),

            // Filter chips
            ClassroomFilterChips(
              selectedFilter: _selectedFilter,
              onFilterChanged: (filter) {
                setState(() {
                  _selectedFilter = filter;
                });
              },
            ),

            SizedBox(height: 16.h),

            // Results count
            Text(
              '${_filteredClassrooms.length} classroom${_filteredClassrooms.length != 1 ? 's' : ''}',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),

            SizedBox(height: 12.h),

            // Classrooms list
            Expanded(
              child: _filteredClassrooms.isEmpty
                  ? _buildEmptyState(theme)
                  : ListView.separated(
                      itemCount: _filteredClassrooms.length,
                      separatorBuilder: (context, index) =>
                          SizedBox(height: 12.h),
                      itemBuilder: (context, index) {
                        final classroom = _filteredClassrooms[index];
                        return ClassroomCard(
                          classroom: classroom,
                          onTap: () => _navigateToClassroomDetail(classroom.id),
                          onMenuAction: (action) =>
                              _handleMenuAction(action, classroom),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _handleCreateClassroom,
        icon: const Icon(Symbols.add),
        label: const Text('New Classroom'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
    );
  }

  /// Build empty state widget when no classrooms match the filter/search
  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Symbols.school,
            size: 64.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          SizedBox(height: 16.h),
          Text(
            _searchQuery.isNotEmpty || _selectedFilter != null
                ? 'No classrooms found'
                : 'No classrooms yet',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            _searchQuery.isNotEmpty || _selectedFilter != null
                ? 'Try adjusting your search or filters'
                : 'Create your first classroom to get started',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Navigate to classroom detail screen
  void _navigateToClassroomDetail(String classroomId) {
    context.pushNamed(
      RouteNames.classroomDetail,
      pathParameters: {'id': classroomId},
    );
  }

  /// Handle menu actions on classroom cards
  void _handleMenuAction(String action, ClassModel classroom) {
    switch (action) {
      case 'edit':
        // TODO: Navigate to edit classroom screen
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Edit classroom feature coming soon')),
        );
        break;
      case 'archive':
        // TODO: Implement archive functionality
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Archived ${classroom.name}')));
        break;
      case 'leave':
        // TODO: Implement leave classroom functionality
        _showLeaveClassroomDialog(classroom);
        break;
      case 'delete':
        // TODO: Implement delete functionality (admin only)
        _showDeleteClassroomDialog(classroom);
        break;
    }
  }

  /// Handle create new classroom action
  void _handleCreateClassroom() {
    // TODO: Navigate to create classroom screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Create classroom feature coming soon')),
    );
  }

  /// Show leave classroom confirmation dialog
  void _showLeaveClassroomDialog(ClassModel classroom) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Classroom'),
        content: Text('Are you sure you want to leave ${classroom.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement actual leave logic
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text('Left ${classroom.name}')));
            },
            child: const Text('Leave'),
          ),
        ],
      ),
    );
  }

  /// Show delete classroom confirmation dialog
  void _showDeleteClassroomDialog(ClassModel classroom) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Classroom'),
        content: Text(
          'Are you sure you want to delete ${classroom.name}? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement actual delete logic
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Deleted ${classroom.name}')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.errorLight),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
