import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../../core/providers/auth_providers.dart';
import '../models/class_model.dart';
import '../enums/classroom_type.dart';
import '../repositories/classroom_repository.dart';

/// Logger for classroom controller
final _logger = Logger();

/// Provider for the classroom repository instance
final classroomRepositoryProvider = Provider<ClassroomRepository>((ref) {
  return ClassroomRepository();
});

/// Provider for current user ID from authentication
final currentUserIdProvider = Provider<String>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.user?.id ?? 'anonymous',
    loading: () => 'anonymous',
    error: (_, __) => 'anonymous',
  );
});

/// Provider to fetch all active classrooms
final allClassroomsProvider = FutureProvider<List<ClassModel>>((ref) async {
  final repository = ref.read(classroomRepositoryProvider);

  try {
    _logger.i('Fetching all active classrooms');
    final classrooms = await repository.getAllClassrooms();
    _logger.i('Successfully fetched ${classrooms.length} classrooms');
    return classrooms;
  } catch (e) {
    _logger.e('Error fetching all classrooms: $e');
    rethrow;
  }
});

/// Provider to fetch classrooms for the current user
final userClassroomsProvider = FutureProvider<List<ClassModel>>((ref) async {
  final repository = ref.read(classroomRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching classrooms for current user: $userId');
    final classrooms = await repository.getClassroomsForStudent(userId);
    _logger.i('Successfully fetched ${classrooms.length} classrooms for user $userId');
    return classrooms;
  } catch (e) {
    _logger.e('Error fetching classrooms for user: $e');
    rethrow;
  }
});

/// Provider to fetch a specific classroom by ID
final classroomDetailProvider = FutureProvider.family<ClassModel?, String>((
  ref,
  classroomId,
) async {
  final repository = ref.read(classroomRepositoryProvider);

  try {
    _logger.i('Fetching classroom detail for ID: $classroomId');
    final classroom = await repository.getClassroomById(classroomId);
    if (classroom != null) {
      _logger.i('Successfully fetched classroom: ${classroom.name}');
    } else {
      _logger.w('Classroom not found: $classroomId');
    }
    return classroom;
  } catch (e) {
    _logger.e('Error fetching classroom detail: $e');
    rethrow;
  }
});

/// Provider to fetch primary classroom for current user
final primaryClassroomProvider = FutureProvider<ClassModel?>((ref) async {
  final repository = ref.read(classroomRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching primary classroom for user: $userId');
    final classroom = await repository.getPrimaryClassroomForStudent(userId);
    if (classroom != null) {
      _logger.i('Successfully fetched primary classroom: ${classroom.name}');
    } else {
      _logger.w('No primary classroom found for user: $userId');
    }
    return classroom;
  } catch (e) {
    _logger.e('Error fetching primary classroom: $e');
    rethrow;
  }
});

/// Provider to fetch classrooms by type
final classroomsByTypeProvider = FutureProvider.family<List<ClassModel>, ClassroomType>((
  ref,
  type,
) async {
  final repository = ref.read(classroomRepositoryProvider);

  try {
    _logger.i('Fetching classrooms by type: ${type.label}');
    final classrooms = await repository.getClassroomsByType(type);
    _logger.i('Successfully fetched ${classrooms.length} ${type.label} classrooms');
    return classrooms;
  } catch (e) {
    _logger.e('Error fetching classrooms by type: $e');
    rethrow;
  }
});

/// State provider for classroom filter type
final classroomFilterProvider = StateProvider<ClassroomType?>((ref) => null);

/// State provider for classroom search query
final classroomSearchProvider = StateProvider<String>((ref) => '');

/// Provider for filtered classrooms based on current filter and search
final filteredClassroomsProvider = FutureProvider<List<ClassModel>>((ref) async {
  final filterType = ref.watch(classroomFilterProvider);
  final searchQuery = ref.watch(classroomSearchProvider);
  
  List<ClassModel> classrooms;
  
  // Get classrooms based on filter
  if (filterType != null) {
    classrooms = await ref.read(classroomsByTypeProvider(filterType).future);
  } else {
    classrooms = await ref.read(userClassroomsProvider.future);
  }
  
  // Apply search filter if query is not empty
  if (searchQuery.isNotEmpty) {
    classrooms = classrooms.where((classroom) {
      final nameMatch = classroom.name.toLowerCase().contains(searchQuery.toLowerCase());
      final subjectMatch = classroom.subject?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false;
      final teacherMatch = classroom.teacherName?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false;
      return nameMatch || subjectMatch || teacherMatch;
    }).toList();
  }
  
  _logger.i('Filtered classrooms: ${classrooms.length} results');
  return classrooms;
});

/// Provider to create a new classroom
final createClassroomProvider = FutureProvider.family<void, ClassModel>((
  ref,
  classroom,
) async {
  return AsyncValue.guard(() async {
    final repository = ref.read(classroomRepositoryProvider);
    
    try {
      _logger.i('Creating new classroom: ${classroom.name}');
      await repository.createClassroom(classroom);
      _logger.i('Successfully created classroom: ${classroom.name}');
      
      // Invalidate relevant providers to refresh data
      ref.invalidate(userClassroomsProvider);
      ref.invalidate(allClassroomsProvider);
    } catch (e) {
      _logger.e('Error creating classroom: $e');
      rethrow;
    }
  });
});

/// Provider to update a classroom
final updateClassroomProvider = FutureProvider.family<void, ClassModel>((
  ref,
  classroom,
) async {
  return AsyncValue.guard(() async {
    final repository = ref.read(classroomRepositoryProvider);
    
    try {
      _logger.i('Updating classroom: ${classroom.name}');
      await repository.updateClassroom(classroom);
      _logger.i('Successfully updated classroom: ${classroom.name}');
      
      // Invalidate relevant providers to refresh data
      ref.invalidate(userClassroomsProvider);
      ref.invalidate(allClassroomsProvider);
      ref.invalidate(classroomDetailProvider(classroom.id));
    } catch (e) {
      _logger.e('Error updating classroom: $e');
      rethrow;
    }
  });
});

/// Provider to delete a classroom
final deleteClassroomProvider = FutureProvider.family<void, String>((
  ref,
  classroomId,
) async {
  return AsyncValue.guard(() async {
    final repository = ref.read(classroomRepositoryProvider);
    
    try {
      _logger.i('Deleting classroom: $classroomId');
      await repository.deleteClassroom(classroomId);
      _logger.i('Successfully deleted classroom: $classroomId');
      
      // Invalidate relevant providers to refresh data
      ref.invalidate(userClassroomsProvider);
      ref.invalidate(allClassroomsProvider);
      ref.invalidate(classroomDetailProvider(classroomId));
    } catch (e) {
      _logger.e('Error deleting classroom: $e');
      rethrow;
    }
  });
});

/// Provider to add student to classroom
final addStudentToClassroomProvider = FutureProvider.family<void, ({String classroomId, String studentId})>((
  ref,
  params,
) async {
  return AsyncValue.guard(() async {
    final repository = ref.read(classroomRepositoryProvider);
    
    try {
      _logger.i('Adding student ${params.studentId} to classroom ${params.classroomId}');
      await repository.addStudentToClassroom(params.classroomId, params.studentId);
      _logger.i('Successfully added student to classroom');
      
      // Invalidate relevant providers to refresh data
      ref.invalidate(userClassroomsProvider);
      ref.invalidate(classroomDetailProvider(params.classroomId));
    } catch (e) {
      _logger.e('Error adding student to classroom: $e');
      rethrow;
    }
  });
});

/// Provider to remove student from classroom
final removeStudentFromClassroomProvider = FutureProvider.family<void, ({String classroomId, String studentId})>((
  ref,
  params,
) async {
  return AsyncValue.guard(() async {
    final repository = ref.read(classroomRepositoryProvider);
    
    try {
      _logger.i('Removing student ${params.studentId} from classroom ${params.classroomId}');
      await repository.removeStudentFromClassroom(params.classroomId, params.studentId);
      _logger.i('Successfully removed student from classroom');
      
      // Invalidate relevant providers to refresh data
      ref.invalidate(userClassroomsProvider);
      ref.invalidate(classroomDetailProvider(params.classroomId));
    } catch (e) {
      _logger.e('Error removing student from classroom: $e');
      rethrow;
    }
  });
});

/// Helper class for classroom controller utilities
class ClassroomControllerUtils {
  /// Invalidate all classroom providers to refresh data
  static void invalidateAllClassroomProviders(WidgetRef ref) {
    ref.invalidate(userClassroomsProvider);
    ref.invalidate(allClassroomsProvider);
    ref.invalidate(primaryClassroomProvider);
    // Note: We don't invalidate family providers as they need specific parameters
    _logger.i('All classroom providers invalidated');
  }
  
  /// Clear all classroom filters
  static void clearAllFilters(WidgetRef ref) {
    ref.read(classroomFilterProvider.notifier).state = null;
    ref.read(classroomSearchProvider.notifier).state = '';
    _logger.i('All classroom filters cleared');
  }
}
